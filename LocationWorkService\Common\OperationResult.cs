﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LocationWorkService.Common
{
    public class OperationResult
    {
        public bool Succeed { get; set; }

        public string Fault { get; set; }

        public string Result { get; set; }
    }

    public class OperationResult<T> : OperationResult where T : class
    {

        public T? ResultObject { get; set; }

    }
}
