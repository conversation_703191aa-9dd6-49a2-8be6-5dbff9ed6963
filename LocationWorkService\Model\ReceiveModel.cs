﻿namespace LocationWorkService.Model
{
    public class ReceiveModel
    {
        public int type { get; set; }

        public string? data { get; set; }

    }

    public class Detail
    {
        //触发的事件类型
        public int type { get; set; }
        public DataModel detail { get; set; }
    }

    public class DataModel
    {

        ////事件状态
        //public int status { get; set; }
        //电子围栏事件中电子围栏名称
        public string fenceName { get; set; }
        //电子围栏事件中进入电子围栏的标签名
        public string entityName { get; set; }
        //电子围栏事件中进入电子围栏的标签设备 uid
        public string tagSn { get; set; }
    }
}
