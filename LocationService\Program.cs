﻿using Microsoft.Extensions.Configuration;
using System.Net.WebSockets;
using System.Text;
using Serilog;

namespace LocationService
{
    class Program
    {
        private static ClientWebSocket webSocket = new ClientWebSocket();
        private static Uri serverUri;
        private static CancellationTokenSource cts = new CancellationTokenSource();

        private static Dictionary<string,string>UWB = new Dictionary<string,string>();

        static async Task Main(string[] args)
        {
            // 配置配置系统
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory) // 设置基础路径，指向输出目录
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true); // 添加JSON配置文件

            IConfigurationRoot configuration = builder.Build();

            // 定义配置类实例
            AppSettings appSettings = new AppSettings();

            // 使用ConfigurationBinder将配置绑定到类
            configuration.Bind(appSettings);
            serverUri = new Uri(appSettings.Settings.URL);
            DatabaseOperator.Initialize(appSettings.Settings.DateBase);

            string logFolder = "logs";
            if (!Directory.Exists(logFolder))
            {
                Directory.CreateDirectory(logFolder);
            }

            Log.Logger = new LoggerConfiguration()
                .WriteTo.File(
                    path: Path.Combine(logFolder, "logfile.txt"),         // 日志文件路径
                    rollingInterval: RollingInterval.Day, // 按天滚动日志文件
                    restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Information, // 最低日志级别
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}", // 日志格式
                    buffered: false, // 禁用缓冲区，实时写入
                    flushToDiskInterval: TimeSpan.FromSeconds(1) // 每秒刷新一次到磁盘
                )
                .CreateLogger();

            Console.WriteLine(DateTime.Now+ ":Connecting to WebSocket server...");
            Log.Information("Connecting to WebSocket server...");


            while (!cts.Token.IsCancellationRequested)
            {
                try
                {
                    await ConnectWebSocketAsync();
                    await SendInitialMessageAsync();
                    await ReceiveMessagesAsync();
                }
                catch (Exception ex)
                {
                    Log.Error(ex.Message);
                }

                Console.WriteLine(DateTime.Now + ":Reconnecting in 3 seconds...");
                Log.Information("Reconnecting in 3 seconds...");
                await Task.Delay(3000);
            }
            Log.CloseAndFlush();
        }

        private static async Task ConnectWebSocketAsync()
        {
            if (webSocket.State != WebSocketState.Open)
            {
                webSocket = new ClientWebSocket();
                await webSocket.ConnectAsync(serverUri, cts.Token);
                Console.WriteLine(DateTime.Now + ":WebSocket connected.");
                Log.Information("WebSocket connected.");
            }
        }

        private static async Task SendInitialMessageAsync()
        {
            // 初始化消息
            var initialMessage = "{\"type\": 3, \"data\": \"{\\\"messageType\\\":7,\\\"messageGroupIds\\\":[\\\"12\\\"],\\\"end\\\":true}\"}";
            var buffer = Encoding.UTF8.GetBytes(initialMessage);
            await webSocket.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Text, true, cts.Token);
            Console.WriteLine("Initial message sent.");
            Log.Information("Initial message sent.");
        }

        private static async Task ReceiveMessagesAsync()
        {
            var buffer = new byte[1024 * 1024];
            var lastMessageTime = DateTime.UtcNow;

            while (webSocket.State == WebSocketState.Open)
            {
                var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cts.Token);

                if (result.MessageType == WebSocketMessageType.Close)
                {
                    await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", cts.Token);
                    Console.WriteLine(DateTime.Now + ":WebSocket closed by server.");
                    Log.Information("WebSocket closed by server.");
                    break;
                }
                else
                {
                    lastMessageTime = DateTime.UtcNow;
                    var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    //Console.WriteLine($"Received: {message}");
                    HandleMessage(message);
                }

                // Check if no message received for 3 seconds
                if ((DateTime.UtcNow - lastMessageTime).TotalSeconds > 3)
                {
                    Console.WriteLine(DateTime.Now + ":No message received for 3 seconds. Reconnecting...");
                    Log.Information("No message received for 3 seconds. Reconnecting...");
                    break;
                }
            }
        }

        private static async void HandleMessage(string message)
        {
            var type = message.ToObject<ReceiveModel>();
            if (type == null) return;
            if (type?.type ==0)return;
            var detail = type?.data.ToObject<Detail>();
            if (detail == null) return;
            if (detail.type != 13) return;
            var data = detail?.detail;
            if (data == null) return;
            if (!UWB.ContainsKey(data.tagSn))
            {
                UWB.Add(data.tagSn,data.fenceName);
                Send(data);
            }
            else
            {
                if (UWB[data.tagSn]== data.fenceName)
                {
                    return;
                }
                else
                {
                    UWB[data.tagSn] = data.fenceName;
                    Send(data);
                }
            }
        }

        private async static Task Send(DataModel dataModel)
        {
            Log.Information("parameter:" + dataModel.ToJson());
            var result= await DatabaseOperator.ExecuteJson("dbo.UWBReportQuantity",dataModel.ToJson());
            Log.Information("result:" + result);
        }
    }
}
