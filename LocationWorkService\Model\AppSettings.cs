﻿using Serilog.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LocationWorkService.Model
{
    public static class AppSettings
    {
        public static Settings Settings { get; set; }
    }


    public class AppSettingModel
    {
        public Settings Settings { get; set; }
    }

    public class Settings
    {
        public string URL { get; set; }
        public string DateBase { get; set; }
        public string LabelUrl { get; set; }
        public string LoginUrl { get; set; }
        public LogEventLevel MinimumLevel { get; set; }
        public int CleanTime { get; set; }
    }
}
