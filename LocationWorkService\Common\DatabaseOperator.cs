﻿using Dapper;
using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper.Contrib.Extensions;

namespace LocationWorkService.Common
{
    public static class DatabaseOperator
    {
        public const string ResultParameter = "Result";

        private static string _ConnectionString;
        public static async Task Initialize(string connectionString)
        {
            _ConnectionString = connectionString;
        }


        public static async Task<T> ExecuteScalar<T>(string sqlQuery, object parameters = null,
            IDbConnection? connection = null, IDbTransaction? transaction = null)
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            var result = await connection.ExecuteScalarAsync<T>(sqlQuery, parameters, transaction, commandTimeout: 3600);

            if (transaction == null)
            {
                connection.Close();
            }

            return result;
        }

        public static async Task<T> Single<T>(string sqlQuery, object parameters = null,
            IDbConnection? connection = null, IDbTransaction? transaction = null)
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            var result = await connection.QuerySingleAsync<T>(sqlQuery, parameters, transaction, commandTimeout: 3600);

            if (transaction == null)
            {
                connection.Close();
            }

            return result;
        }

        public static async Task ExecuteSingle(string sql, object model, IDbConnection? connection = null,
            IDbTransaction? transaction = null)
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            await connection.ExecuteAsync(sql, model, transaction, 3600, CommandType.Text);

            if (transaction == null)
            {
                connection.Close();
            }
        }

        public static async Task<string?> ExecuteJson(string storeProcedure, string parameter, IDbConnection? connection = null,
            IDbTransaction? transaction = null)
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            var dynamicPara = GetResultDynamicParameters(parameter);
            await connection.ExecuteAsync(storeProcedure, dynamicPara, transaction, commandType: CommandType.StoredProcedure, commandTimeout: 3600);
            var resultString = dynamicPara.Get<string>(ResultParameter);

            if (transaction == null)
            {
                connection.Close();
            }

            return resultString;
        }

        public static async Task Insert<T>(T entity, IDbConnection? connection = null,
            IDbTransaction? transaction = null) where T : class
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            await connection.InsertAsync<T>(entity, transaction, commandTimeout: 3600);

            if (transaction == null)
            {
                connection.Close();
            }
        }


        public static async Task Update<T>(T entity, IDbConnection? connection = null,
            IDbTransaction? transaction = null) where T : class
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            await connection.UpdateAsync<T>(entity, transaction, commandTimeout: 3600);

            if (transaction == null)
            {
                connection.Close();
            }
        }

        public static async Task<IEnumerable<T>> Query2<T>(string sqlQuery, IDbConnection? connection = null,
            IDbTransaction? transaction = null, IDictionary<string, object>? parameters = null) where T : class
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            var result = await connection.QueryAsync<T>(sqlQuery, parameters, transaction, commandTimeout: 3600);

            if (transaction == null)
            {
                connection.Close();
            }

            return result;
        }


        public static async Task<IEnumerable<T>> Query<T>(string storeProcedure, string parameter = "",
            IDbConnection? connection = null, IDbTransaction? transaction = null)
        {
            if (string.IsNullOrEmpty(parameter))
            {
                parameter = "{}";
            }

            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            var result = connection.Query<T>(storeProcedure, new { Parameter = parameter },
                commandType: CommandType.StoredProcedure, commandTimeout: 3600);

            if (transaction == null)
            {
                connection.Close();
            }

            return result;
        }

        public static async Task DoTransaction(Action<IDbConnection, IDbTransaction> action)
        {
            using var connection = GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            using var transaction = connection.BeginTransaction();

            try
            {
                action.Invoke(connection, transaction);
                transaction.Commit();
            }
            catch (Exception e)
            {
                transaction.Rollback();
                throw;
            }
            finally
            {
                connection.Close();
            }
        }


        public static async Task<int> Execute(string sql, object para = null, IDbConnection? connection = null, IDbTransaction? transaction = null)
        {
            connection ??= GetConnection();

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            var result = await connection.ExecuteAsync(sql, para, transaction, commandTimeout: 3600);
            if (transaction == null)
            {
                connection.Close();
            }

            return result;
        }

        private static SqlConnection GetConnection()
        {
            return new SqlConnection(_ConnectionString);
        }


        private static DynamicParameters GetResultDynamicParameters(string parameter)
        {
            var dynamicPara = new DynamicParameters(new { Parameter = parameter });
            dynamicPara.Add(ResultParameter, string.Empty, DbType.String, ParameterDirection.Output, int.MaxValue);
            return dynamicPara;
        }
    }
}
