﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LocationWorkService.Model;

namespace LocationWorkService.Common
{
    public  static class AccessToken
    {
        public static AccessTokenModel AccessTokenModel { get; set; } = new AccessTokenModel();
        public static DateTime OverDueTime  { get; set; } = DateTime.Now.AddDays(-1);
    }
}
