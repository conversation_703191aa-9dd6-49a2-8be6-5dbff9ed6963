﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Threading.Tasks;

namespace LocationWorkService.Common
{
    public static class JsonExtension
    {
        public static string? ToJson<T>(this T? obj, JsonSerializerOptions? option = null)
        {
            if (obj == null)
            {
                return null;
            }

            return JsonSerializer.Serialize(obj, option);
        }

        public static string? ToLogJson<T>(this T? obj)
        {
            if (obj == null)
            {
                return null;
            }

            return JsonSerializer.Serialize(obj, new JsonSerializerOptions()
            {
                Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
        }

        public static T? ToObject<T>(this string? obj) where T : class
        {
            if (obj == null)
            {
                return default(T);
            }

            return JsonSerializer.Deserialize<T>(obj);
        }

        public static T? TryToObject<T>(this string? obj) where T : class
        {
            if (obj == null)
            {
                return default;
            }

            try
            {
                return JsonSerializer.Deserialize<T>(obj);
            }
            catch (Exception)
            {
                return default;
            }

        }
    }
}
