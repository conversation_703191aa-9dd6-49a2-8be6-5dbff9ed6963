﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace LocationWorkService.Common
{
    public class IncHttpUtility
    {
        private const int TimeOutSeconds = 10;


        private static HttpClient GetHttpClient()
        {
            HttpClientHandler handler = new HttpClientHandler()
            {
                AutomaticDecompression = DecompressionMethods.GZip,
                CheckCertificateRevocationList = false,
                ClientCertificateOptions = ClientCertificateOption.Manual,
                ServerCertificateCustomValidationCallback = (_, _, _, _) => true
            };

            var httpClient = new HttpClient(handler)
            {
                Timeout = TimeSpan.FromSeconds(TimeOutSeconds),
            };

            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            httpClient.DefaultRequestHeaders.Add("Access-Control-Allow-Origin", "*");
            httpClient.DefaultRequestHeaders.Add("X-Frame-Options", "ALLOW-FROM *");

            return httpClient;
        }

        public static async Task<string> Post(string url, dynamic parameter, Dictionary<string, string>? headers = null,
            string token = "")
        {
            var httpClient = GetHttpClient();

            if (headers != null)
            {
                foreach (var httpRequestHeader in headers)
                {
                    httpClient.DefaultRequestHeaders.Add(httpRequestHeader.Key, httpRequestHeader.Value);
                }
            }

            if (!string.IsNullOrEmpty(token))
            {
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }

            var settings = new JsonSerializerSettings
            {
                StringEscapeHandling = StringEscapeHandling.EscapeHtml
            };
            string jsonRequest = JsonConvert.SerializeObject(parameter, settings);

            //var jsonRequest = JsonSerializer.Serialize(parameter);
            var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(url, content).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadAsStringAsync();
        }
    }
}
