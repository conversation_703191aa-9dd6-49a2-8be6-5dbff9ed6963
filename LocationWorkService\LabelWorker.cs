using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Threading.Tasks;
using LocationWorkService.Common;
using LocationWorkService.Model;
using System.Reflection.Metadata;
using System.Reflection;
using static System.Net.WebRequestMethods;
using System.Transactions;

namespace LocationWorkService
{
    public class LabelWorker : BackgroundService
    {
        private readonly ILogger<LabelWorker> _logger;

        private static string Name => "LabelWorker:";

        public LabelWorker(ILogger<LabelWorker> logger)
        {
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (true)
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(AccessToken.AccessTokenModel.accessToken)|| AccessToken.OverDueTime<=DateTime.Now)
                    {
                        var parameter1 = new 
                        {
                            username = "admin",
                            password = "nsq123456"

                        };
                        var access =
                            await IncHttpUtility.Post(AppSettings.Settings.LoginUrl,
                                parameter1);
                        var accessresult = access.ToObject<LabelResult<AccessTokenModel>>();
                        if (accessresult.succeed)
                        {
                            AccessToken.AccessTokenModel.accessToken = accessresult.data.accessToken;
                            AccessToken.OverDueTime = DateTime.Now.AddMinutes(accessresult.data.expiration);
                        }
                    }

                    var result = await DatabaseOperator.ExecuteJson("dbo.ReFreshlabel", "");
                    Log.Information(Name + "result:" + result);
                    var temp = result.ToObject<OperationResult<List<LabelModel>>>();
                    temp.ResultObject = temp.Result?.ToObject<List<LabelModel>>() ?? new List<LabelModel>();
                    var Models = temp.ResultObject;
                    foreach (var model in Models)
                    {
                        try
                        {
                            var parameter = new SendDataModel()
                            {
                                id = model.PageId,
                                deviceId = model.DeviceId,
                                extraProperties = new List<ExtraProperty>()
                            {
                                new ExtraProperty()
                                {
                                    name = "当前人员",
                                    value = model.UserName
                                },
                                new ExtraProperty()
                                {
                                    name = "当前工单",
                                    value = model.ShopOrderNo
                                }
                            }
                            };
                            var shoporders = model.ShopOrderList.ToObject<List<ShopOrderModel>>();
                            if (shoporders != null && shoporders.Count > 0)
                            {
                                foreach (var shoporder in shoporders)
                                {
                                    var index = shoporders.IndexOf(shoporder) + 1;
                                    if (index > 10)
                                    {
                                        return;
                                    }
                                    parameter.extraProperties.Add(new ExtraProperty()
                                    {
                                        name = index.ToString() + ".",
                                        value = shoporder.ShopOrderNo
                                    });
                                }
                            }
                            var http =
                                await IncHttpUtility.Post(AppSettings.Settings.LabelUrl,
                                    parameter, token: AccessToken.AccessTokenModel.accessToken);
                            var httpresult = http.ToObject<LabelResult<bool>>();
                            //Log.Information(parameter.ToJson());
                            if (!httpresult.succeed)
                            {
                                Log.Warning(Name + http);
                                string updateSql = "UPDATE dbo.LabelRefresh SET handle =0 WHERE DBPK = @DBPK";
                                var updateParams = new { DBPK = model.DBPK };
                                await DatabaseOperator.Execute(updateSql, updateParams);
                            }
                            //else
                            //{
                            //    string updateSql = "UPDATE dbo.LabelRefresh SET handle =1 WHERE DBPK = @DBPK";
                            //    var updateParams = new { DBPK = model.DBPK };
                            //    await DatabaseOperator.Execute(updateSql, updateParams);
                            //}
                            await Task.Delay(500);
                        }
                        catch (Exception e)
                        {
                            Log.Error(Name + e.Message);
                            continue;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(Name + ex.Message);
                }
                finally
                {
                    await Task.Delay(20*1000);
                }
            }
        }
    }
}
